# Request Logs 自动上传模块

## 功能概述

该模块实现了将 request-logs 自动上传到 CDN 的功能，包括：

1. **自动定时任务**：每天凌晨 00:05:00 自动上传前一天的 request-logs 到 CDN
2. **手动上传接口**：提供 API 接口支持手动上传指定日期或日期范围的日志文件
3. **文件管理**：查看本地可用的日志文件列表

## 定时任务

- **执行时间**：每天凌晨 00:05:00
- **执行逻辑**：上传前一天的完整日志文件到 CDN
- **文件路径**：`logs/request-logs/YYYY-MM-DD.jsonl`
- **CDN 路径**：`/request-logs/YYYY-MM-DD.jsonl`

## API 接口

### 1. 手动上传指定日期的日志

```http
POST /log-upload/upload-by-date
Content-Type: application/json

{
  "date": "2024-01-15"
}
```

**响应示例**：
```json
{
  "code": 1,
  "message": "上传成功",
  "data": "https://f2.eckwai.com/fangzhou/material-platform-metrics-service/request-logs/2024-01-15.jsonl"
}
```

### 2. 批量上传日期范围的日志

```http
POST /log-upload/upload-by-date-range
Content-Type: application/json

{
  "startDate": "2024-01-10",
  "endDate": "2024-01-15"
}
```

**响应示例**：
```json
{
  "code": 1,
  "message": "批量上传完成，成功上传 6 个文件",
  "data": [
    "https://f2.eckwai.com/fangzhou/material-platform-metrics-service/request-logs/2024-01-10.jsonl",
    "https://f2.eckwai.com/fangzhou/material-platform-metrics-service/request-logs/2024-01-11.jsonl",
    "..."
  ]
}
```

### 3. 获取本地可用的日志文件列表

```http
GET /log-upload/available-files
```

**响应示例**：
```json
{
  "code": 1,
  "message": "获取成功",
  "data": [
    "2024-01-10.jsonl",
    "2024-01-11.jsonl",
    "2024-01-12.jsonl"
  ]
}
```

### 4. 手动触发定时任务（测试用）

```http
POST /log-upload/trigger-daily-upload
```

**响应示例**：
```json
{
  "code": 1,
  "message": "定时任务执行完成"
}
```

## 错误处理

- 文件不存在时会记录警告日志并跳过
- 文件为空时会记录警告日志并跳过
- CDN 上传失败时会记录错误日志
- 所有操作都有详细的日志记录

## 日志记录

所有操作都会记录到应用日志中，包括：
- 定时任务的执行状态
- 文件上传的成功/失败状态
- CDN URL 信息
- 错误详情

## 注意事项

1. **文件路径**：确保 `logs/request-logs` 目录存在且有读取权限
2. **CDN 配置**：确保 CDN 配置正确，上传功能正常
3. **磁盘空间**：定时任务不会自动删除本地文件，需要根据需要手动清理
4. **主服务实例**：定时任务只在主服务实例中执行，避免重复上传

## 部署说明

1. 模块已自动注册到 `AppModule` 中
2. 启动服务后定时任务会自动生效
3. 可通过 API 接口进行手动操作和测试
